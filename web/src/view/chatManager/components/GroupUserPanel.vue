<template>
  <div class="group-user-panel">
    <div class="panel-header">
      <h4>群成员 ({{ members.length }})</h4>
    </div>
    
    <div class="member-list">
      <el-scrollbar class="member-scrollbar">
        <div class="member-item" v-for="member in members" :key="member.id">
          <div class="member-info" @click="handleMemberClick(member)">
            <div class="avatar-wrapper">
              <el-avatar :size="32" :src="member.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div 
                v-if="member.online" 
                class="online-dot"
              ></div>
            </div>
            
            <div class="member-details">
              <div class="member-name">
                {{ member.nickname }}
                <el-tag v-if="member.isAdmin" size="small" type="warning">管理员</el-tag>
              </div>
              <div class="member-status">
                <span :class="member.online ? 'online' : 'offline'">
                  {{ member.online ? '在线' : '离线' }}
                </span>
              </div>
            </div>
          </div>
          
          <el-dropdown 
            @command="handleCommand"
            trigger="click"
            class="member-actions"
          >
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="`chat_${member.id}`">
                  <el-icon><ChatDotRound /></el-icon>
                  私聊
                </el-dropdown-item>
                <el-dropdown-item :command="`profile_${member.id}`">
                  <el-icon><User /></el-icon>
                  查看资料
                </el-dropdown-item>
                <el-dropdown-item 
                  v-if="canManage && !member.isAdmin" 
                  :command="`admin_${member.id}`"
                  divided
                >
                  <el-icon><Star /></el-icon>
                  设为管理员
                </el-dropdown-item>
                <el-dropdown-item 
                  v-if="canManage" 
                  :command="`kick_${member.id}`"
                  class="danger-item"
                >
                  <el-icon><Remove /></el-icon>
                  移出群聊
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
  name: 'GroupUserPanel'
})

const props = defineProps({
  group: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['member-click', 'start-chat'])

// 模拟群成员数据
const members = ref([
  {
    id: 'user1',
    nickname: '张三',
    avatar: '',
    online: true,
    isAdmin: true,
    joinTime: '2024-01-01'
  },
  {
    id: 'user2',
    nickname: '李四',
    avatar: '',
    online: true,
    isAdmin: false,
    joinTime: '2024-01-02'
  },
  {
    id: 'user3',
    nickname: '王五',
    avatar: '',
    online: false,
    isAdmin: false,
    joinTime: '2024-01-03'
  },
  {
    id: 'user4',
    nickname: '赵六',
    avatar: '',
    online: true,
    isAdmin: false,
    joinTime: '2024-01-04'
  },
  {
    id: 'user5',
    nickname: '钱七',
    avatar: '',
    online: false,
    isAdmin: false,
    joinTime: '2024-01-05'
  }
])

// 当前用户是否有管理权限
const canManage = computed(() => {
  // 这里应该根据实际的权限逻辑判断
  return true
})

// 处理成员点击
const handleMemberClick = (member) => {
  emit('member-click', member)
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  const [action, userId] = command.split('_')
  const member = members.value.find(m => m.id === userId)
  
  if (!member) return
  
  switch (action) {
    case 'chat':
      startPrivateChat(member)
      break
    case 'profile':
      showMemberProfile(member)
      break
    case 'admin':
      setAsAdmin(member)
      break
    case 'kick':
      kickMember(member)
      break
  }
}

// 开始私聊
const startPrivateChat = (member) => {
  emit('start-chat', member)
  ElMessage.success(`开始与 ${member.nickname} 的私聊`)
}

// 查看成员资料
const showMemberProfile = (member) => {
  ElMessage.info(`查看 ${member.nickname} 的资料`)
  // 这里应该打开用户资料弹窗
}

// 设为管理员
const setAsAdmin = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 ${member.nickname} 设为管理员吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API设置管理员
    member.isAdmin = true
    ElMessage.success(`${member.nickname} 已设为管理员`)
  } catch {
    // 用户取消操作
  }
}

// 移出群聊
const kickMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 ${member.nickname} 移出群聊吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API移出成员
    const index = members.value.findIndex(m => m.id === member.id)
    if (index > -1) {
      members.value.splice(index, 1)
    }
    ElMessage.success(`${member.nickname} 已被移出群聊`)
  } catch {
    // 用户取消操作
  }
}
</script>

<style lang="scss" scoped>
.group-user-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #374151;
  color: #f3f4f6;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #6b7280;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #f3f4f6;
  }
}

.member-list {
  flex: 1;
  overflow: hidden;

  .member-scrollbar {
    height: 100%;
  }
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  transition: background-color 0.2s;

  &:hover {
    background: rgba(75, 85, 99, 0.6);
  }

  .member-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;

    .avatar-wrapper {
      position: relative;

      .online-dot {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 10px;
        height: 10px;
        background: #22c55e;
        border: 2px solid #374151;
        border-radius: 50%;
      }
    }

    .member-details {
      flex: 1;
      min-width: 0;

      .member-name {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #f3f4f6;
        margin-bottom: 2px;

        .el-tag {
          font-size: 10px;
          height: 16px;
          line-height: 14px;
        }
      }

      .member-status {
        font-size: 12px;

        .online {
          color: #22c55e;
        }

        .offline {
          color: #9ca3af;
        }
      }
    }
  }

  .member-actions {
    .el-button {
      color: #9ca3af;

      &:hover {
        color: #f3f4f6;
      }
    }
  }
}

:deep(.el-dropdown-menu__item) {
  &.danger-item {
    color: #e74c3c;
    
    &:hover {
      background: #e74c3c;
      color: white;
    }
  }
}
</style>
