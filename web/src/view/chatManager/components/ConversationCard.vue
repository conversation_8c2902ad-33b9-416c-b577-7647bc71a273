<template>
  <div class="conversation-card" :class="{ active: conversation.active }" @click="handleClick">
    <el-badge :value="conversation.unread" :show-zero="false" class="conversation-badge">
      <div class="card-content">
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <el-avatar :size="40" :src="conversation.avatar">
              <el-icon v-if="conversation.type === 'group'"><ChatLineRound /></el-icon>
              <el-icon v-else><User /></el-icon>
            </el-avatar>
            <div 
              v-if="conversation.type === 'friend' && conversation.online" 
              class="online-dot"
            ></div>
          </div>
        </div>
        
        <div class="content-section">
          <div class="name-row">
            <span class="name">{{ conversation.name }}</span>
            <span class="time">{{ conversation.lastTime }}</span>
          </div>
          <div class="message-row">
            <div class="last-message">
              <span v-if="conversation.lastMessage">{{ conversation.lastMessage }}</span>
              <span v-else class="no-message">暂无消息</span>
            </div>
          </div>
        </div>
      </div>
    </el-badge>
  </div>
</template>

<script setup>
// Element Plus 图标已全局注册，无需导入

defineOptions({
  name: 'ConversationCard'
})

const props = defineProps({
  conversation: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click', props.conversation)
}
</script>

<style lang="scss" scoped>
.conversation-card {
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(75, 85, 99, 0.6);
  }

  &.active {
    background: rgba(34, 197, 94, 0.2);
    border-left: 3px solid #22c55e;
  }

  .conversation-badge {
    width: 100%;

    :deep(.el-badge__content) {
      right: 8px;
      top: 8px;
      background: #22c55e;
      border: none;
      font-size: 11px;
      min-width: 16px;
      height: 16px;
      line-height: 16px;
    }
  }

  .card-content {
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 12px;
  }

  .avatar-section {
    .avatar-wrapper {
      position: relative;

      .online-dot {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 10px;
        height: 10px;
        background: #22c55e;
        border: 2px solid #111827;
        border-radius: 50%;
      }
    }
  }

  .content-section {
    flex: 1;
    min-width: 0;

    .name-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;

      .name {
        color: #f3f4f6;
        font-weight: 500;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }

      .time {
        color: #9ca3af;
        font-size: 11px;
        margin-left: 8px;
        white-space: nowrap;
      }
    }

    .message-row {
      .last-message {
        color: #9ca3af;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;

        .no-message {
          font-style: italic;
          opacity: 0.7;
        }
      }
    }
  }
}
</style>
