// 聊天相关样式
.chat-dialog {
  .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }

  .el-dialog__header {
    padding: 0;
    margin: 0;
  }

  .el-dialog__body {
    padding: 0;
  }
}

// 会话卡片样式
.conversation-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &.active {
    background-color: rgba(52, 152, 219, 0.3);
  }
}

// 消息气泡样式
.message-bubble {
  max-width: 70%;
  word-wrap: break-word;
  border-radius: 12px;
  position: relative;
  
  &.own-bubble {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    margin-left: auto;
    
    &::after {
      content: '';
      position: absolute;
      right: -6px;
      top: 10px;
      width: 0;
      height: 0;
      border-left: 6px solid #3498db;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }
  
  &.other-bubble {
    background: white;
    color: #2c3e50;
    border: 1px solid #e0e0e0;
    
    &::before {
      content: '';
      position: absolute;
      left: -6px;
      top: 10px;
      width: 0;
      height: 0;
      border-right: 6px solid white;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }
}

// 在线状态点
.online-dot {
  width: 12px;
  height: 12px;
  background: #27ae60;
  border: 2px solid #2c3e50;
  border-radius: 50%;
  position: absolute;
  bottom: 2px;
  right: 2px;
}

// 滚动条样式
.el-scrollbar__bar {
  &.is-vertical {
    right: 2px;
    width: 6px;
    
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chat-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 2.5vh auto !important;
      height: 95vh !important;
    }
  }
  
  .conversation-sidebar {
    width: 240px !important;
  }
  
  .group-members {
    width: 180px !important;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-item {
  animation: fadeIn 0.3s ease-out;
}

// 表情选择器样式
.emoji-picker {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  
  .emoji-item {
    transition: all 0.2s ease;
    
    &:hover {
      background: #f0f0f0;
      transform: scale(1.1);
    }
  }
}
