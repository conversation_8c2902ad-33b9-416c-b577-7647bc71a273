<template>
  <div class="message-status">
    <!-- 发送中 -->
    <el-icon v-if="status === 'sending'" class="status-icon sending">
      <Loading />
    </el-icon>
    
    <!-- 发送成功 -->
    <el-icon v-else-if="status === 'sent'" class="status-icon sent">
      <Check />
    </el-icon>
    
    <!-- 已读 -->
    <div v-else-if="status === 'read'" class="status-icon read">
      <el-icon><Check /></el-icon>
      <el-icon><Check /></el-icon>
    </div>
    
    <!-- 发送失败 -->
    <el-icon v-else-if="status === 'failed'" class="status-icon failed">
      <Close />
    </el-icon>
  </div>
</template>

<script setup>
// Element Plus 图标已全局注册，无需导入

defineOptions({
  name: 'MessageStatus'
})

defineProps({
  status: {
    type: String,
    default: 'sent', // sending, sent, read, failed
    validator: (value) => ['sending', 'sent', 'read', 'failed'].includes(value)
  }
})
</script>

<style lang="scss" scoped>
.message-status {
  display: inline-flex;
  align-items: center;
  font-size: 12px;

  .status-icon {
    &.sending {
      color: #f59e0b;
      animation: rotate 1s linear infinite;
    }

    &.sent {
      color: #9ca3af;
    }

    &.read {
      color: #22c55e;
      display: flex;
      align-items: center;

      .el-icon:last-child {
        margin-left: -4px;
      }
    }

    &.failed {
      color: #ef4444;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
