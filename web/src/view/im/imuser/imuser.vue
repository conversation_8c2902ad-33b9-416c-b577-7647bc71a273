<template>
	<div>
		<div class="gva-search-box">
			<el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline"
				:rules="searchRule" @keyup.enter="onSubmit">
				<el-form-item label="昵称" prop="name">
					<el-input v-model="searchInfo.name" placeholder="搜索条件" />
				</el-form-item>
				<el-form-item label="是否在线" prop="online">
					<el-select v-model="searchInfo.online" clearable placeholder="请选择">
						<el-option key="true" label="是" value="true">
						</el-option>
						<el-option key="false" label="否" value="false">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="是否为管理员" prop="isAdmin">
					<el-select v-model="searchInfo.isAdmin" clearable placeholder="请选择">
						<el-option key="true" label="是" value="true">
						</el-option>
						<el-option key="false" label="否" value="false">
						</el-option>
					</el-select>
				</el-form-item>

				<template v-if="showAllQuery">
					<!-- 将需要控制显示状态的查询条件添加到此范围内 -->
				</template>

				<el-form-item>
					<el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
					<el-button icon="refresh" @click="onReset">重置</el-button>

				</el-form-item>
			</el-form>
		</div>
		<div class="gva-table-box">
			<!--       <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div> -->
			<el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="id"
				@selection-change="handleSelectionChange" @sort-change="sortChange">
				<el-table-column type="selection" width="55" />

				<el-table-column sortable align="left" label="ID" prop="id" width="80" />
				<el-table-column sortable align="left" label="创建时间" prop="createdAt" width="180">
					<template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
				</el-table-column>
				<el-table-column sortable align="left" label="更新时间" prop="updatedAt" width="180">
					<template #default="scope">{{ formatDate(scope.row.updatedAt) }}</template>
				</el-table-column>
				<el-table-column align="left" label="手机号码" prop="iphoneNum" width="120" />
				<el-table-column align="left" label="昵称" prop="name" width="120" />
				<el-table-column align="left" label="头像" prop="headImg" width="60">
					<template #default="scope">
						<el-image v-if="scope.row.headImg" style="width: 30px; height: 30px"
							:src="scope.row.headImg"></el-image>
					</template>
				</el-table-column>
				<el-table-column align="left" label="加入的群组" prop="groups" width="120" />
				<!-- <el-table-column align="left" label="更多详情" prop="moreInfo" width="120" /> -->
				<el-table-column sortable align="left" label="是否在线" prop="online" width="120">
					<template #default="scope">{{ formatBoolean(scope.row.online) }}</template>
				</el-table-column>
				<!-- <el-table-column align="left" label="其它" prop="other" width="120" /> -->
				<el-table-column sortable align="left" label="是否为管理员" prop="isAdmin" width="140">
					<template #default="scope">
						<el-switch @change="changeAdmin(scope.row)" v-model="scope.row.isAdmin" active-color="#13ce66"
							inactive-color="#ff4949" active-text="是" inactive-text="否" clearable></el-switch>
					</template>
				</el-table-column>
				<!-- <el-table-column align="left" label="用户配置" prop="userConfig" width="120" /> -->
				<el-table-column sortable align="left" label="最后登录时间" prop="lastLoginTime" width="180">
					<template #default="scope">{{ formatDate(scope.row.lastLoginTime) }}</template>
				</el-table-column>
				<el-table-column align="center" label="操作" fixed="right" :min-width="appStore.operateMinWith"
					width="">
					<template #default="scope">
						<el-button type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon
								style="margin-right: 5px">
								<InfoFilled />
							</el-icon>查看</el-button>
							<el-button type="primary" link class="table-button" @click="sendMessage(scope.row)"><el-icon
								style="margin-right: 5px">
								<InfoFilled />
							</el-icon>发送消息</el-button>
						<!--      <el-button  type="primary" link icon="edit" class="table-button" @click="updateImUserFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button> -->
					</template>
				</el-table-column>
			</el-table>
			<div class="gva-pagination">
				<el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page"
					:page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :total="total"
					@current-change="handleCurrentChange" @size-change="handleSizeChange" />
			</div>
		</div>
		<el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false"
			:before-close="closeDialog">
			<template #header>
				<div class="flex justify-between items-center">
					<span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
					<div>
						<el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
						<el-button @click="closeDialog">取 消</el-button>
					</div>
				</div>
			</template>

			<el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
				<el-form-item label="ID:" prop="id">
					<el-input v-model.number="formData.id" :clearable="true" placeholder="请输入ID" />
				</el-form-item>
				<el-form-item label="创建时间:" prop="createdAt">
					<el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期"
						:clearable="true" />
				</el-form-item>
				<el-form-item label="更新时间:" prop="updatedAt">
					<el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期"
						:clearable="true" />
				</el-form-item>
				<el-form-item label="手机号码:" prop="iphoneNum">
					<el-input v-model="formData.iphoneNum" :clearable="true" placeholder="请输入手机号码" />
				</el-form-item>
				<el-form-item label="昵称:" prop="name">
					<el-input v-model="formData.name" :clearable="true" placeholder="请输入昵称" />
				</el-form-item>
				<el-form-item label="头像:" prop="headImg">
					<el-input v-model="formData.headImg" :clearable="true" placeholder="请输入头像" />
				</el-form-item>
				<el-form-item label="加入的群组:" prop="groups">
					<el-input v-model="formData.groups" :clearable="true" placeholder="请输入加入的群组" />
				</el-form-item>
				<el-form-item label="更多详情:" prop="moreInfo">
					<el-input v-model="formData.moreInfo" :clearable="true" placeholder="请输入更多详情" />
				</el-form-item>
				<el-form-item label="是否在线:" prop="online">
					<el-switch v-model="formData.online" active-color="#13ce66" inactive-color="#ff4949" active-text="是"
						inactive-text="否" clearable></el-switch>
				</el-form-item>
				<el-form-item label="其它:" prop="other">
					<el-input v-model="formData.other" :clearable="true" placeholder="请输入其它" />
				</el-form-item>
				<el-form-item label="是否为管理员:" prop="isAdmin">
					<el-switch v-model="formData.isAdmin" active-color="#13ce66" inactive-color="#ff4949"
						active-text="是" inactive-text="否" clearable></el-switch>
				</el-form-item>
				<el-form-item label="用户配置:" prop="userConfig">
					<el-input v-model="formData.userConfig" :clearable="true" placeholder="请输入用户配置" />
				</el-form-item>
				<el-form-item label="最后登录时间:" prop="lastLoginTime">
					<el-date-picker v-model="formData.lastLoginTime" type="date" style="width:100%" placeholder="选择日期"
						:clearable="true" />
				</el-form-item>
			</el-form>
		</el-drawer>

		<el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true"
			:before-close="closeDetailShow" title="查看">
			<el-descriptions :column="1" border>
				<el-descriptions-item label="ID">
					{{ detailFrom.id }}
				</el-descriptions-item>
				<el-descriptions-item label="创建时间">
					{{ detailFrom.createdAt }}
				</el-descriptions-item>
				<el-descriptions-item label="更新时间">
					{{ detailFrom.updatedAt }}
				</el-descriptions-item>
				<el-descriptions-item label="手机号码">
					{{ detailFrom.iphoneNum }}
				</el-descriptions-item>
				<el-descriptions-item label="昵称">
					{{ detailFrom.name }}
				</el-descriptions-item>
				<el-descriptions-item label="头像">
					<el-image v-if="detailFrom.headImg" :preview-src-list="[detailFrom.headImg]"
						style="width: 50px; height: 50px" :src="detailFrom.headImg"></el-image>
				</el-descriptions-item>
				<el-descriptions-item label="加入的群组">
					{{ detailFrom.groups }}
				</el-descriptions-item>
				<el-descriptions-item label="更多详情">
					{{ detailFrom.moreInfo }}
				</el-descriptions-item>
				<el-descriptions-item label="是否在线">
					{{ detailFrom.online }}
				</el-descriptions-item>
				<el-descriptions-item label="其它">
					{{ detailFrom.other }}
				</el-descriptions-item>
				<el-descriptions-item label="是否为管理员">
					{{ detailFrom.isAdmin }}
				</el-descriptions-item>
				<el-descriptions-item label="用户配置">
					{{ detailFrom.userConfig }}
				</el-descriptions-item>
				<el-descriptions-item label="最后登录时间">
					{{ detailFrom.lastLoginTime }}
				</el-descriptions-item>
			</el-descriptions>
		</el-drawer>

	</div>
</template>

<script setup>
	import {
		createImUser,
		deleteImUser,
		deleteImUserByIds,
		updateImUser,
		findImUser,
		getImUserList
	} from '@/api/im/imuser'

	// 全量引入格式化工具 请按需保留
	import {
		getDictFunc,
		formatDate,
		formatBoolean,
		filterDict,
		filterDataSource,
		returnArrImg,
		onDownloadFile
	} from '@/utils/format'
	import {
		ElMessage,
		ElMessageBox
	} from 'element-plus'
	import {
		ref,
		reactive
	} from 'vue'
	import {
		useAppStore
	} from "@/pinia"



	defineOptions({
		name: 'ImUser'
	})

	// 提交按钮loading
	const btnLoading = ref(false)
	const appStore = useAppStore()

	// 控制更多查询条件显示/隐藏状态
	const showAllQuery = ref(false)

	// 自动化生成的字典（可能为空）以及字段
	const formData = ref({
		id: undefined,
		createdAt: new Date(),
		updatedAt: new Date(),
		iphoneNum: '',
		name: '',
		headImg: '',
		groups: '',
		moreInfo: '',
		online: false,
		other: '',
		isAdmin: false,
		userConfig: '',
		lastLoginTime: new Date(),
	})



	// 验证规则
	const rule = reactive({})

	const searchRule = reactive({
		createdAt: [{
			validator: (rule, value, callback) => {
				if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
					callback(new Error('请填写结束日期'))
				} else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
					callback(new Error('请填写开始日期'))
				} else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (
						searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt
						.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value
						.endCreatedAt.getTime())) {
					callback(new Error('开始日期应当早于结束日期'))
				} else {
					callback()
				}
			},
			trigger: 'change'
		}],
	})

	const elFormRef = ref()
	const elSearchFormRef = ref()

	// =========== 表格控制部分 ===========
	const page = ref(1)
	const total = ref(0)
	const pageSize = ref(10)
	const tableData = ref([])
	const searchInfo = ref({})
	// 排序
	const sortChange = ({
		prop,
		order
	}) => {
		const sortMap = {
			id: 'id',
			createdAt: 'created_at',
			updatedAt: 'updated_at',
			online: 'online',
			isAdmin: 'is_admin',
			lastLoginTime: 'last_login_time',
		}

		let sort = sortMap[prop]
		if (!sort) {
			sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
		}

		searchInfo.value.sort = sort
		searchInfo.value.order = order
		getTableData()
	}
	// 重置
	const onReset = () => {
		searchInfo.value = {}
		getTableData()
	}

	// 搜索
	const onSubmit = () => {
		elSearchFormRef.value?.validate(async (valid) => {
			if (!valid) return
			page.value = 1
			if (searchInfo.value.online === "") {
				searchInfo.value.online = null
			}
			if (searchInfo.value.isAdmin === "") {
				searchInfo.value.isAdmin = null
			}
			getTableData()
		})
	}

	// 分页
	const handleSizeChange = (val) => {
		pageSize.value = val
		getTableData()
	}

	// 修改页面容量
	const handleCurrentChange = (val) => {
		page.value = val
		getTableData()
	}

	// 查询
	const getTableData = async () => {
		const table = await getImUserList({
			page: page.value,
			pageSize: pageSize.value,
			...searchInfo.value
		})
		if (table.code === 0) {
			tableData.value = table.data.list
			total.value = table.data.total
			page.value = table.data.page
			pageSize.value = table.data.pageSize
		}
	}

	getTableData()

	// ============== 表格控制部分结束 ===============

	// 获取需要的字典 可能为空 按需保留
	const setOptions = async () => {}

	// 获取需要的字典 可能为空 按需保留
	setOptions()


	// 多选数据
	const multipleSelection = ref([])
	// 多选
	const handleSelectionChange = (val) => {
		multipleSelection.value = val
	}

	// 删除行
	const deleteRow = (row) => {
		ElMessageBox.confirm('确定要删除吗?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			deleteImUserFunc(row)
		})
	}

	// 多选删除
	const onDelete = async () => {
		ElMessageBox.confirm('确定要删除吗?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(async () => {
			const ids = []
			if (multipleSelection.value.length === 0) {
				ElMessage({
					type: 'warning',
					message: '请选择要删除的数据'
				})
				return
			}
			multipleSelection.value &&
				multipleSelection.value.map(item => {
					ids.push(item.id)
				})
			const res = await deleteImUserByIds({
				ids
			})
			if (res.code === 0) {
				ElMessage({
					type: 'success',
					message: '删除成功'
				})
				if (tableData.value.length === ids.length && page.value > 1) {
					page.value--
				}
				getTableData()
			}
		})
	}

	// 行为控制标记（弹窗内部需要增还是改）
	const type = ref('')

	// 更新行
	const updateImUserFunc = async (row) => {
		const res = await findImUser({
			id: row.id
		})
		type.value = 'update'
		if (res.code === 0) {
			formData.value = res.data
			dialogFormVisible.value = true
		}
	}


	// 删除行
	const deleteImUserFunc = async (row) => {
		const res = await deleteImUser({
			id: row.id
		})
		if (res.code === 0) {
			ElMessage({
				type: 'success',
				message: '删除成功'
			})
			if (tableData.value.length === 1 && page.value > 1) {
				page.value--
			}
			getTableData()
		}
	}

	// 弹窗控制标记
	const dialogFormVisible = ref(false)

	// 打开弹窗
	const openDialog = () => {
		type.value = 'create'
		dialogFormVisible.value = true
	}

	// 关闭弹窗
	const closeDialog = () => {
		dialogFormVisible.value = false
		formData.value = {
			id: undefined,
			createdAt: new Date(),
			updatedAt: new Date(),
			iphoneNum: '',
			name: '',
			headImg: '',
			groups: '',
			moreInfo: '',
			online: false,
			other: '',
			isAdmin: false,
			userConfig: '',
			lastLoginTime: new Date(),
		}
	}
	// 弹窗确定
	const enterDialog = async () => {
		btnLoading.value = true
		elFormRef.value?.validate(async (valid) => {
			if (!valid) return btnLoading.value = false
			let res
			switch (type.value) {
				case 'create':
					res = await createImUser(formData.value)
					break
				case 'update':
					res = await updateImUser(formData.value)
					break
				default:
					res = await createImUser(formData.value)
					break
			}
			btnLoading.value = false
			if (res.code === 0) {
				ElMessage({
					type: 'success',
					message: '创建/更改成功'
				})
				closeDialog()
				getTableData()
			}
		})
	}

	const detailFrom = ref({})

	// 查看详情控制标记
	const detailShow = ref(false)


	// 打开详情弹窗
	const openDetailShow = () => {
		detailShow.value = true
	}


	// 打开详情
	const getDetails = async (row) => {
		// 打开弹窗
		const res = await findImUser({
			id: row.id
		})
		if (res.code === 0) {
			detailFrom.value = res.data
			openDetailShow()
		}
	}


	// 关闭详情弹窗
	const closeDetailShow = () => {
		detailShow.value = false
		detailFrom.value = {}
	}
	const changeAdmin = (row) => {
		console.log(row)
		if (row.isAdmin) {
			ElMessageBox.confirm('确定要将当前用户设置为管理员吗?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				updateImUser(row).then(res=>{
					if (res.code === 0) {
						ElMessage({
						  type: 'success',
						  message: '设置成功'
						})
					}
				})
				
			}).catch(() => {
			  // 用户点击"取消"的回调
			  row.isAdmin = false
			  // 这里可以执行取消后的操作
			})
		}
		
	}
	/**
	 * 发送消息
	 * 私聊、打开聊天框
	 * @param row
	 */
	const sendMessage = (row) => {
		console.log('私聊、打开聊天框',row)
		// 触发全局事件，打开聊天弹窗
		import('@/utils/bus.js').then(({ emitter }) => {
			emitter.emit('openChatDialog', row)
		})
	}
	
</script>

<style>

</style>