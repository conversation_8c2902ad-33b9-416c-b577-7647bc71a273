<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天弹窗更新演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .updates-list {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .updates-list h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .update-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #27ae60;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .update-icon {
            color: #27ae60;
            margin-right: 15px;
            font-weight: bold;
            font-size: 18px;
        }
        
        .update-content h4 {
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        
        .update-content p {
            color: #7f8c8d;
            margin: 0;
            line-height: 1.5;
        }
        
        .demo-preview {
            border: 2px solid #3498db;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 30px;
            height: 650px;
            display: flex;
            position: relative;
        }
        
        .height-indicator {
            position: absolute;
            right: -40px;
            top: 0;
            bottom: 0;
            width: 30px;
            background: #e74c3c;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            writing-mode: vertical-lr;
            font-size: 12px;
            font-weight: bold;
        }
        
        .sidebar-demo {
            width: 350px;
            background: #2c3e50;
            display: flex;
            flex-direction: row;
        }
        
        .tabs-demo {
            width: 80px;
            background: #1a252f;
            display: flex;
            flex-direction: column;
        }
        
        .tab-item-demo {
            padding: 20px 10px;
            color: #bdc3c7;
            text-align: center;
            border-bottom: 1px solid #34495e;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tab-item-demo.active {
            background: #3498db;
            color: white;
        }
        
        .tab-item-demo:hover {
            background: #34495e;
            color: white;
        }
        
        .tab-icon {
            font-size: 20px;
            margin-bottom: 8px;
        }
        
        .conversations-demo {
            flex: 1;
            background: #2c3e50;
            padding: 10px;
        }
        
        .conversation-item {
            background: rgba(255,255,255,0.1);
            margin-bottom: 8px;
            padding: 12px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .conversation-item.active {
            background: rgba(52, 152, 219, 0.3);
        }
        
        .conversation-item:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .chat-area-demo {
            flex: 1;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 18px;
            position: relative;
        }
        
        .empty-state {
            text-align: center;
            color: #95a5a6;
        }
        
        .group-panel-demo {
            width: 200px;
            background: #34495e;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #bdc3c7;
            font-size: 14px;
        }
        
        .behavior-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .behavior-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .behavior-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .behavior-step {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 10px;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 20px;
            }
            
            .behavior-demo {
                grid-template-columns: 1fr;
            }
            
            .demo-preview {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🔄 聊天弹窗更新演示</h1>
            <p>弹窗高度固定650px + 群聊好友切换逻辑优化</p>
        </div>
        
        <div class="updates-list">
            <h3>📋 本次更新内容</h3>
            
            <div class="update-item">
                <div class="update-icon">1️⃣</div>
                <div class="update-content">
                    <h4>弹窗高度固定650px</h4>
                    <p>将弹窗高度从响应式90vh改为固定650px，确保在不同屏幕尺寸下保持一致的用户体验</p>
                </div>
            </div>
            
            <div class="update-item">
                <div class="update-icon">2️⃣</div>
                <div class="update-content">
                    <h4>群聊/好友切换逻辑优化</h4>
                    <p>切换到好友时显示"请选择一个会话开始聊天"的空状态，切换到群聊时自动显示唯一群聊的聊天页面</p>
                </div>
            </div>
            
            <div class="update-item">
                <div class="update-icon">3️⃣</div>
                <div class="update-content">
                    <h4>默认显示群聊</h4>
                    <p>弹窗打开时默认显示群聊标签页，并自动选择第一个群聊会话</p>
                </div>
            </div>
        </div>
        
        <div class="demo-preview">
            <div class="height-indicator">650px</div>
            <div class="sidebar-demo">
                <div class="tabs-demo">
                    <div class="tab-item-demo active" onclick="switchTab('groups')">
                        <div class="tab-icon">💬</div>
                        <div>群聊</div>
                    </div>
                    <div class="tab-item-demo" onclick="switchTab('friends')">
                        <div class="tab-icon">👤</div>
                        <div>好友</div>
                    </div>
                </div>
                <div class="conversations-demo">
                    <div id="groupConversations">
                        <div class="conversation-item active">📱 Chatterbox (聊天室)</div>
                    </div>
                    <div id="friendConversations" style="display: none;">
                        <div class="conversation-item">👤 张三</div>
                        <div class="conversation-item">👤 李四</div>
                        <div class="conversation-item">👤 王五</div>
                    </div>
                </div>
            </div>
            <div class="chat-area-demo">
                <div id="chatContent">群聊聊天界面</div>
                <div id="emptyState" class="empty-state" style="display: none;">
                    <div style="font-size: 48px; margin-bottom: 16px;">📭</div>
                    <div>请选择一个会话开始聊天</div>
                </div>
            </div>
            <div class="group-panel-demo" id="groupPanel">
                群组成员
            </div>
        </div>
        
        <div class="behavior-demo">
            <div class="behavior-card">
                <h4>🔄 切换到好友标签页</h4>
                <div class="behavior-step">
                    <div class="step-number">1</div>
                    <span>点击"好友"标签</span>
                </div>
                <div class="behavior-step">
                    <div class="step-number">2</div>
                    <span>清空当前选择的会话</span>
                </div>
                <div class="behavior-step">
                    <div class="step-number">3</div>
                    <span>显示"请选择一个会话开始聊天"</span>
                </div>
                <div class="behavior-step">
                    <div class="step-number">4</div>
                    <span>隐藏群组成员面板</span>
                </div>
            </div>
            
            <div class="behavior-card">
                <h4>💬 切换到群聊标签页</h4>
                <div class="behavior-step">
                    <div class="step-number">1</div>
                    <span>点击"群聊"标签</span>
                </div>
                <div class="behavior-step">
                    <div class="step-number">2</div>
                    <span>自动选择第一个群聊</span>
                </div>
                <div class="behavior-step">
                    <div class="step-number">3</div>
                    <span>显示群聊聊天界面</span>
                </div>
                <div class="behavior-step">
                    <div class="step-number">4</div>
                    <span>显示群组成员面板</span>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; padding: 20px; background: #e8f5e8; border-radius: 8px;">
            <h3>✅ 更新完成</h3>
            <p>弹窗高度已固定为650px，群聊/好友切换逻辑已优化，用户体验更加流畅</p>
        </div>
    </div>
    
    <script>
        function switchTab(tab) {
            // 更新标签页样式
            document.querySelectorAll('.tab-item-demo').forEach(item => {
                item.classList.remove('active');
            });
            
            if (tab === 'groups') {
                document.querySelector('.tab-item-demo:first-child').classList.add('active');
                document.getElementById('groupConversations').style.display = 'block';
                document.getElementById('friendConversations').style.display = 'none';
                document.getElementById('chatContent').style.display = 'block';
                document.getElementById('emptyState').style.display = 'none';
                document.getElementById('groupPanel').style.display = 'flex';
            } else {
                document.querySelector('.tab-item-demo:last-child').classList.add('active');
                document.getElementById('groupConversations').style.display = 'none';
                document.getElementById('friendConversations').style.display = 'block';
                document.getElementById('chatContent').style.display = 'none';
                document.getElementById('emptyState').style.display = 'block';
                document.getElementById('groupPanel').style.display = 'none';
            }
        }
    </script>
</body>
</html>
